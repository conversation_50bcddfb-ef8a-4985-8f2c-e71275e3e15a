<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class MakeZipNullableInAllAddressTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        try {
            // Check if the addresses table exists
            if (Schema::hasTable('addresses')) {
                // Make zip nullable
                DB::statement('ALTER TABLE addresses MODIFY zip VARCHAR(191) NULL');
                echo "Successfully made 'zip' column nullable in addresses table\n";
            }
        } catch (\Exception $e) {
            echo "Error making zip nullable in addresses table: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        try {
            // Check if the addresses table exists
            if (Schema::hasTable('addresses')) {
                // Revert zip to not nullable
                DB::statement('ALTER TABLE addresses MODIFY zip VARCHAR(191) NOT NULL');
                echo "Successfully reverted 'zip' column to NOT NULL in addresses table\n";
            }
        } catch (\Exception $e) {
            echo "Error reverting zip in addresses table: " . $e->getMessage() . "\n";
        }
    }
}
