<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class MakeZipNullableAlternative extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        try {
            Schema::table('addresses', function (Blueprint $table) {
                $table->string('zip')->nullable()->change();
            });
            echo "Successfully made 'zip' column nullable in addresses table using Schema::table\n";
        } catch (\Exception $e) {
            echo "Error making zip nullable in addresses table: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        try {
            Schema::table('addresses', function (Blueprint $table) {
                $table->string('zip')->nullable(false)->change();
            });
            echo "Successfully reverted 'zip' column to NOT NULL in addresses table\n";
        } catch (\Exception $e) {
            echo "Error reverting zip in addresses table: " . $e->getMessage() . "\n";
        }
    }
}
