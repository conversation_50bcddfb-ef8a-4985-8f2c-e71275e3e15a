@import "../vendors/css/bootstrap.min.css";
@import "font-awesome/css/font-awesome";
@import "nprogress/nprogress";
@import "./datatables";
@import "./selectize";
@import "./toaster.scss";
@import "./ohsnap";
@import "./flatpickr";
@import "./classes";
@import "./tab";
@import "./alert";
@import "./modal";
@import "./utilities";
@import "./accordion";
@import "./fleetcart";
@import "./panel";

html {
    direction: ltr;
}

.btn-actions {
    margin: 0 15px 15px 0;

    > i {
        margin-right: 5px;
    }
}

.overflow-hidden {
    overflow: hidden;
}

#nprogress {
    .bar {
        background: #0068e1;
    }

    .peg {
        box-shadow: 0 0 10px #0068e1, 0 0 5px #0068e1;
    }

    .spinner-icon {
        border-top-color: #0068e1;
        border-left-color: #0068e1;
    }
}

.sortable-ghost {
    opacity: 0.8;
}

.btn-group.open .dropdown-toggle,
.btn-group .dropdown-toggle:active {
    -webkit-box-shadow: none;
    box-shadow: none;
}

.options {
    &.table > {
        thead > {
            tr > {
                th {
                    padding: 6px 8px;
                }
            }
        }
    }

    tr td:first-child {
        width: 35px;
        min-width: 35px;
    }

    tr td:last-child {
        width: 60px;
    }

    .drag-handle {
        font-size: 16px;
        color: #737881;
        vertical-align: top;
        margin-top: 11px;
        white-space: nowrap;
        display: inline-block;
        cursor: move;

        i {
            float: left;

            &:nth-child(2) {
                margin-left: 1px;
            }
        }
    }

    .choose-file,
    .delete-row {
        padding: 8px 14px;
    }

    .delete-row {
        color: #4d4d4d;
    }
}

.pagination {
    margin-bottom: 10px;

    > li {
        float: left;
        margin-left: -1px;
        padding: 0;
        border: 1px solid #e9e9e9;
        transition: 150ms ease-in-out;

        &:hover {
            cursor: pointer;
            background: #f1f1f1;
        }

        &:first-child {
            margin-left: 0;
            border-radius: 3px 0 0 3px;

            a {
                border-radius: 3px 0 0 3px;
            }
        }

        &:last-child {
            border-radius: 0 3px 3px 0;

            a {
                border-radius: 0 3px 3px 0;
            }
        }

        &.disabled {
            cursor: not-allowed !important;

            &:hover {
                background: transparent;
            }
        }

        > a {
            padding: 10px 15px;
            color: #0068e1;
            border: none;
            display: block;
            background: transparent !important;
            text-decoration: none;

            &:hover {
                background: transparent;
            }

            &:hover,
            &:focus {
                color: #0068e1;
            }
        }

        > span {
            display: block;
            padding: 10px 15px;
            border: none;
        }
    }

    > .active {
        background: #0068e1;
        cursor: default !important;
        border-color: #0068e1;

        &:hover {
            background: #0068e1;
        }

        > a {
            background: transparent !important;
        }

        > span {
            background: transparent !important;
        }
    }
}

.empty {
    color: #626060;
}

[v-cloak] {
    display: none;
}
