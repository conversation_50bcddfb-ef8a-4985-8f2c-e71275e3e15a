.modal {
    z-index: 100000;
    background: rgba(0, 0, 0, 0.6);
    z-index: 9999;
    transition: 0.2s ease-in-out;

    &.fade {
        .modal-dialog {
            opacity: 0;
            transform: scale(0.9);
            transition: 100ms ease-in-out;
        }

        &.in .modal-dialog {
            transform: scale(1);
            opacity: 1;
        }

        &.show {
            opacity: 1;

            .modal-dialog {
                transform: scale(1);
                opacity: 1;
            }
        }
    }

    .modal-header {
        padding: 15px;
        border-bottom-color: #e9e9e9;

        > .close {
            font-size: 18px;
            outline: 0;
            margin-top: -1px;
            -webkit-text-stroke: 1px #ffffff;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: darken(#f1f1f1, 6%);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 0.8;
            transition: 150ms ease-in-out;

            svg {
                width: 14px;
                height: 14px;

                path {
                    stroke: #444;
                    transition: 150ms ease-in-out;
                }
            }

            &:hover {
                background: lighten(#fc4b4b, 30%);
                opacity: 1;

                svg {
                    path {
                        stroke: #fc4b4b;
                    }
                }
            }
        }
    }

    .modal-content {
        border: none;
        border-radius: 8px;
        box-shadow: 0 2px 3px rgba(0, 0, 0, 0.12);
    }

    .modal-footer {
        border-radius: 0 0 8px 8px;
        border-top-color: #e9e9e9;

        .btn + .btn {
            margin-left: 12px;
        }
    }
}

#confirmation-modal {
    padding-right: 0 !important;

    .modal-header {
        padding: 15px 15px 15px 25px;
    }

    .modal-body {
        padding: 0px 15px 25px 25px;
    }

    .modal-header,
    .modal-footer {
        border: none;
    }

    .default-message {
        color: #737881;
    }

    .modal-footer {
        background: #f1f3f7;
    }

    .btn-default {
        background: #e1e1e1;

        &:hover {
            background: #e9e9e9;
        }
    }
}
