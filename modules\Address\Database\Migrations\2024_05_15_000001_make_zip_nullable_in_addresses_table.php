<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class MakeZipNullableInAddressesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Make zip nullable
        DB::statement('ALTER TABLE addresses MODIFY zip VARCHAR(191) NULL');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Revert zip to not nullable
        DB::statement('ALTER TABLE addresses MODIFY zip VARCHAR(191) NOT NULL');
    }
}
