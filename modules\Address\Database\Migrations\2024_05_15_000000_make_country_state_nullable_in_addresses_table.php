<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class MakeCountryStateNullableInAddressesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Make state nullable
        DB::statement('ALTER TABLE addresses MODIFY state VARCHAR(191) NULL');
        
        // Make country nullable
        DB::statement('ALTER TABLE addresses MODIFY country VARCHAR(191) NULL');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Revert state to not nullable
        DB::statement('ALTER TABLE addresses MODIFY state VARCHAR(191) NOT NULL');
        
        // Revert country to not nullable
        DB::statement('ALTER TABLE addresses MODIFY country VARCHAR(191) NOT NULL');
    }
}
