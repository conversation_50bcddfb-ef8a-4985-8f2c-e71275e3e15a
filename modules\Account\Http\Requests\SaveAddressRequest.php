<?php

namespace Modules\Account\Http\Requests;

use Modules\Support\Country;
use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\Request;

class SaveAddressRequest extends Request
{
    /**
     * Available attributes.
     *
     * @var string
     */
    protected $availableAttributes = 'account::attributes.addresses';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'first_name' => ['required'],
            'last_name' => ['required'], // Last name is required by the database
            'address_1' => ['required'],
            'city' => ['required'],
            'phone' => ['required', 'string', 'min:9', 'max:15'], // Ensure phone is required and validated
            'phone_2' => ['nullable', 'string', 'min:9', 'max:15'], // Ensure phone_2 is validated (nullable)
            // 'zip' => ['required'], // Assuming zip/state/country are optional or handled differently
            // 'country' => ['required', Rule::in(Country::supportedCodes())],
            // 'state' => ['required'],
        ];
    }
}
